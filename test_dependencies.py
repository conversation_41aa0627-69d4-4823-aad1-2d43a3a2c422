#!/usr/bin/env python3
"""
测试脚本：验证所有依赖包是否正确安装
"""

def test_imports():
    """测试所有必要的包是否可以正常导入"""
    try:
        print("测试导入 pandas...")
        import pandas as pd
        print("✓ pandas 导入成功")
        
        print("测试导入 PIL (Pillow)...")
        from PIL import Image
        print("✓ PIL (Pillow) 导入成功")
        
        print("测试导入 pytesseract...")
        import pytesseract
        print("✓ pytesseract 导入成功")
        
        print("\n所有依赖包导入成功！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    try:
        print("\n测试 pandas 基本功能...")
        import pandas as pd
        df = pd.DataFrame({'test': [1, 2, 3]})
        print(f"✓ pandas DataFrame 创建成功: {len(df)} 行")
        
        print("测试 PIL 基本功能...")
        from PIL import Image
        # 创建一个简单的测试图像
        img = Image.new('RGB', (100, 100), color='red')
        print("✓ PIL 图像创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始测试依赖包...")
    print("=" * 50)
    
    if test_imports() and test_basic_functionality():
        print("\n" + "=" * 50)
        print("🎉 所有测试通过！依赖包安装正确。")
        print("现在可以运行主程序了。")
    else:
        print("\n" + "=" * 50)
        print("❌ 测试失败，请检查依赖包安装。")
