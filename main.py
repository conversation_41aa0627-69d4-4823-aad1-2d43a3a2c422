import pytesseract
from PIL import Image
import pandas as pd

# Set the path to the Tesseract executable (adjust based on your system)
# For macOS with Homebrew installation, Tesseract is usually in /usr/local/bin/tesseract
# If Tesseract is not yet installed, the program will show an error message
try:
    # Try to use the default tesseract command (should work if installed via Homebrew)
    import subprocess
    subprocess.run(['tesseract', '--version'], capture_output=True, check=True)
    print("✓ Tesseract OCR 已找到并可用")
except (subprocess.CalledProcessError, FileNotFoundError):
    print("❌ 警告: Tesseract OCR 未安装或不在 PATH 中")
    print("请运行以下命令安装 Tesseract:")
    print("brew install tesseract")
    print("安装完成后重新运行此程序。")
    exit(1)

# Load the image
image_path = 'Weixin Image_20250818232359_4_21.jpg'  # Replace with the actual image path
image = Image.open(image_path)

# Perform OCR to extract text
text = pytesseract.image_to_string(image)

# Initialize lists to store data
store_names = []
ratings = []
monthly_sales = []
avg_prices = []
delivery_times = []

# Process the extracted text (simplified parsing based on expected format)
lines = text.split('\n')
for line in lines:
    parts = line.split()
    if len(parts) >= 5:  # Assuming each line has store name, rating, sales, price, and time
        store_names.append(' '.join(parts[:-4]))
        ratings.append(float(parts[-4]) if parts[-4].replace('.', '').isdigit() else 0.0)
        monthly_sales.append(int(parts[-3].replace('月售', '')) if '月售' in parts[-3] else 0)
        avg_prices.append(float(parts[-2].replace('元', '')) if '元' in parts[-2] else 0.0)
        delivery_times.append(int(parts[-1].replace('分钟', '')) if '分钟' in parts[-1] else 0)

# Create a DataFrame
data = {
    '店名': store_names,
    '评分': ratings,
    '月售数量': monthly_sales,
    '人均价格 (元)': avg_prices,
    '配送时间 (分钟)': delivery_times
}
df = pd.DataFrame(data)

# Calculate monthly revenue
df['月营业额 (元)'] = df['月售数量'] * df['人均价格 (元)']

# Sort by monthly revenue in descending order
df = df.sort_values(by='月营业额 (元)', ascending=False)

# Display the result
print(df)

# Optional: Save to CSV
df.to_csv('store_analysis_result.csv', index=False)