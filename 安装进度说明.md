# 项目依赖安装进度说明

## 项目概述
这是一个餐厅数据分析项目，使用 OCR 技术从图片中提取餐厅信息并进行数据分析。

## 已完成的安装步骤

### 1. Python 环境设置 ✅
- 安装了 Python 3.12.11
- 使用 `uv` 作为包管理器
- 设置了项目的 Python 版本

### 2. Python 依赖包安装 ✅
已成功安装以下 Python 包：
- `pandas>=2.3.1` - 数据分析库
- `pillow>=11.3.0` - 图像处理库 (PIL)
- `pytesseract>=0.3.13` - Tesseract OCR Python 接口

### 3. 依赖测试 ✅
- 创建了 `test_dependencies.py` 测试脚本
- 所有 Python 依赖包都能正常导入和使用
- 基本功能测试通过

### 4. 代码适配 ✅
- 修改了 `main.py` 中的 Tesseract 路径设置
- 添加了 macOS 兼容性检查
- 程序现在会自动检测 Tesseract 是否安装

## 正在进行的安装步骤

### 5. Tesseract OCR 安装 🔄
- 正在通过 Homebrew 安装 Tesseract OCR
- 包含大量依赖项，需要编译时间较长
- 当前进度：正在安装依赖项（已完成多个依赖项的安装）

## 下一步操作

### 等待 Tesseract 安装完成
1. Tesseract 安装完成后，程序将能够完整运行
2. 可以使用以下命令检查安装状态：
   ```bash
   brew list tesseract
   tesseract --version
   ```

### 运行程序
安装完成后，可以使用以下命令运行程序：
```bash
uv run python main.py
```

### 测试程序功能
1. 确保项目目录中有测试图片 `Weixin Image_20250818232359_4_21.jpg`
2. 程序将：
   - 使用 OCR 提取图片中的文本
   - 解析餐厅信息（店名、评分、月售数量、人均价格、配送时间）
   - 计算月营业额
   - 按营业额排序
   - 输出结果并保存为 CSV 文件

## 项目文件结构
```
resturant/
├── main.py                    # 主程序
├── test_dependencies.py       # 依赖测试脚本
├── pyproject.toml            # 项目配置和依赖
├── uv.lock                   # 依赖锁定文件
├── .python-version           # Python 版本文件
├── Weixin Image_20250818232359_4_21.jpg  # 测试图片
└── 安装进度说明.md            # 本文档
```

## 故障排除

### 如果 Tesseract 安装失败
可以尝试以下方法：
1. 取消当前安装：`Ctrl+C`
2. 清理 Homebrew 缓存：`brew cleanup`
3. 重新安装：`brew install tesseract`

### 如果程序运行出错
1. 检查 Tesseract 是否正确安装：`tesseract --version`
2. 检查图片文件是否存在
3. 运行依赖测试：`uv run python test_dependencies.py`

## 预期结果
程序成功运行后，将输出类似以下格式的餐厅分析结果：
- 店名、评分、月售数量、人均价格、配送时间、月营业额
- 按月营业额降序排列
- 同时生成 `store_analysis_result.csv` 文件
